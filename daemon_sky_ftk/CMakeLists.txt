cmake_minimum_required(VERSION 3.16)
project(daemon_sky_ftk)

set(CMAKE_CXX_STANDARD 98)

# 컴파일 데이터베이스 생성 (IDE/에디터의 코드 탐색 기능을 위해)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

message(STATUS "Starting CMake configuration for daemon_sky_ftk")

# OpenSSL 설정 (간소화)
set(OPENSSL_LIBS "-lcrypto -lssl")

# Oracle 환경 설정 (간소화)
if(DEFINED ENV{ORACLE_HOME})
    set(ORACLE_HOME $ENV{ORACLE_HOME})
    message(STATUS "Using ORACLE_HOME from environment: ${ORACLE_HOME}")
else()
    set(ORACLE_HOME "/usr/lib/oracle/21/client64")
    message(STATUS "Using default ORACLE_HOME: ${ORACLE_HOME}")
endif()

# Oracle include 경로 설정 (존재 여부 확인)
if(EXISTS "/usr/include/oracle/21/client64")
    set(ORACLE_INCLUDES "-I/usr/include/oracle/21/client64")
    message(STATUS "Oracle include directory found: /usr/include/oracle/21/client64")
else()
    set(ORACLE_INCLUDES "")
    message(WARNING "Oracle include directory not found: /usr/include/oracle/21/client64")
endif()
# Oracle 라이브러리 경로
set(ORACLE_LIBS "-L${ORACLE_HOME}/lib")

# Pro*C 실행파일 찾기 (선택사항)
find_program(PROC_EXECUTABLE proc
    PATHS ${ORACLE_HOME}/bin /usr/bin /usr/local/bin
    DOC "Oracle Pro*C compiler"
)

if(PROC_EXECUTABLE)
    message(STATUS "Found Pro*C compiler: ${PROC_EXECUTABLE}")
else()
    message(STATUS "Pro*C compiler not found - will build without Pro*C support")
    set(PROC_EXECUTABLE "")
endif()

# 데이터베이스 설정 (간소화 - 테스트용)
set(DBSTRING "test_db")
set(DBID "test_user")
set(DBPASS "test_pass")

message(STATUS "Using test database configuration")

# Include directories (기본적인 것만)
include_directories(
    inc
    lib
)

# 외부 라이브러리 include (존재하는 경우에만)
if(EXISTS "${CMAKE_SOURCE_DIR}/../command_kskyb_ftk/inc")
    include_directories(${CMAKE_SOURCE_DIR}/../command_kskyb_ftk/inc)
    message(STATUS "Added command_kskyb_ftk include directory")
endif()

if(EXISTS "${CMAKE_SOURCE_DIR}/../libsrc/libkskyb/inc")
    include_directories(${CMAKE_SOURCE_DIR}/../libsrc/libkskyb/inc)
    message(STATUS "Added libkskyb include directory")
endif()

# 컴파일 정의
add_definitions(
    -D_GNU_SOURCE
    -D_REENTRANT
    -DDEBUG=5
)

# 컴파일 플래그
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g")

# 간단한 라이브러리 생성 (Pro*C 없이)
add_library(daemon_sky_lib STATIC
    lib/Properties.cpp
    lib/SocketTCP.cpp
    lib/ksbase64.cpp
    lib/PacketCtrlSKY.cpp
    lib/myException.cpp
    lib/Encrypt.cpp
    lib/DatabaseORA.cpp
)

# 메인 실행파일
add_executable(telco_sky_new
    src/telco_sky_new.cpp
)

# 라이브러리 링크
target_link_libraries(telco_sky_new daemon_sky_lib)

# 기본 시스템 라이브러리만 링크
target_link_libraries(telco_sky_new
    pthread
    dl
    nsl
    crypto
    ssl
)

# 출력 디렉토리 설정
set_target_properties(telco_sky_new
    PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/bin"
)

message(STATUS "CMake configuration completed successfully")
