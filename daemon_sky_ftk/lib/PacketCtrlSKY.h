/*
 * PacketCtrlSKY.h
 *
 *  Created on: 2009. 9. 2.
 *      Author: Administrator
 */

#ifndef PACKETCTRLSKY_H_
#define PACKETCTRLSKY_H_

#include "ksbase64.h"
#include "myException.h"
#include "seed.h"
#include "AESEncryption.h"
#include "Properties.h"
#include <stdlib.h>

#include <vector>
#include <string>
#include <iostream>

#include <ml_ctrlsub.h>

using namespace std;

#define LMS_TEXT_BUFF_SIZE 1024
#define CTNPATHOFFSET 6

typedef struct _SNDSKY
{
	char s_tran_pr      	 [32   +1];
	char s_tran_id      	 [20   +1];
	char s_tran_phone   	 [15   +1];
	char s_tran_msg        [2000 +1];
	int  s_tran_type;
	char s_tran_tmpl_cd 	 [30   +1];
	char s_tran_sender_key [40   +1];
	char s_tran_button     [4000 +1];
	char s_ttl             [14   +1];
	char s_tran_date       [14   +1];
	char s_tran_title      [50   +1];
	char s_tran_price      [10   +1];
	char s_tran_curtype    [3    +1];
	char s_tran_method     [8    +1];
	char s_tran_timeout    [5    +1];
	char s_tran_callback   [15	 +1];
	char s_rep_flag	       [1    +1];
	char s_rep_title       [200  +1];
	char s_rep_msg        [2000  +1];
	char s_tran_introlink  [4000 +1];
	char s_reserved			[4000 +1];
	// Additional fields for FTALKUP support
	char s_encoding        [20   +1];
	char s_chat_bubble_type[30   +1];
	char s_targeting       [1    +1];
	char s_app_user_id     [20   +1];
	char s_push_alarm      [1    +1];
	char s_message_variable[2000 +1];
	char s_button_variable [2000 +1];
	char s_coupon_variable [1000 +1];
	char s_image_variable  [1000 +1];
	char s_video_variable  [1000 +1];
	char s_commerce_variable[1000 +1];
	char s_carousel_variable[2000 +1];
} SNDSKY;


typedef struct _HEADER {			/* KskyB 전문 Header */
	int msgType;
	int msgLeng;
	char msgVer[4];
} HEADER;

typedef struct MSG_BIND_SND	{		/* 접속요청 전문 [Client -> KskyB] */
	HEADER header;
	char szCID[16];
	char szPWD[16];
} BIND_REQ;

typedef struct MSG_BIND_ACK {		/* 접속요청에 대한 응답 [KT -> Client] */
	HEADER header;
	int nResult;
	int nSchSP;
	int nSchSQ;
} BIND_ACK;

typedef struct MSG_DATA_SND	{		/* 메세지 전송 전문 [Client -> KT] */
	HEADER header;
	int nMsgId;
	int nReport;
	int nExpireTime;
	int nSchTyp;
	int	nRcptDataLen;
	int nRerv;
	char szSchSTime[12];
	char szSchDTime[12];
	char szCalBakNum[32];
	char szAni[32];
	char szCalBakUrl[160];
	char szSmsMsg[160];
	char szCdrId[16];
	char szKtOff[12];
  char szRcptData[16];
} DELIVER_REQ; /* header 12 byte body 462 = 470 */

typedef struct MSG_DATA_ACK {		/* 응답전문 [KT -> Client] */
	HEADER header;
	int nResult;
	int nJobId;
	int nMsgId;
} DELIVER_ACK;

typedef struct MSG_TRNS_REP	{		/* 메세지 단말기 수신결과 전문 (레포트 전문 ) [KT -> Client] */
	HEADER header;
	int nResult;
	int nJobId;
	int nSubJobId;
	int nMsgId;
	int nFee;
	char szDelvTm[16];
	char szTelInfo[8];
} REPORT;

typedef struct MSG_REPORT_ACK {		/* 응답전문 [Client -> KT] */
	HEADER header;
	int nResult;
	int nJobId;
	int nSubJobId;
} REPORT_ACK;

typedef struct MSG_LINK_CHK	{
	HEADER header;
	int nSeq;
} LINK_CHK;

typedef struct MSG_LINK_CHK_ACK	{
	HEADER header;
	int nSeq;
	int nResult;
} LINK_CHK_ACK;

class CPacketCtrlSKY
{
public:
	enum logLevels {
		TYPE_BIND_REQ = 2,	/* Bind 요구		*/
		TYPE_BIND_ACK,		/* Bind 응답		*/
		TYPE_DELIVER_REQ,	/* 전송요청		*/
		TYPE_DELIVER_ACK,	/* SubmitAck	*/
		TYPE_REPORT,		/* 전송결과		*/
		TYPE_REPORT_ACK,	/* 전송결과Ack	*/
		TYPE_PING,			/* 상태확인		*/
		TYPE_PONG			/* 상태확인Ack	*/
	};

   CPacketCtrlSKY() : encryptionInitialized(false) {};
	virtual ~CPacketCtrlSKY() {};
	
	char*		m_ptrBuff;		 
	char*     m_ptrData;		// encoded data
	
	int getMsg_BIND_REQ(char* pBuff, char* id, char* pw, int nTp);
	int getMsg_PING_REQ(char* pBuff);
	int getMsg_PING_RES(char* pBuff, string sKey);
	//int getMsg_DELIVER_REQ(string& sSndBuff, long long nMsgSeq, vector<string>& vtSend);
	int getMsg_DELIVER_REQ(string& sSndBuff, SNDSKY& vtSend);
	int getMsg_REPORT_ACK(char* pBuff, string sKey);
	int getData_BndAck(char* pBuff, vector<string>& vtBndAck);
	int getData_SndAck(char* pBuff, vector<string>& vtSndAck);
	int getData_Report(char* pBuff, vector<string>& vtReport);
	int getMsgCode(char* pBuff);

	char* matchString(char* szOrg, char* szTag, string &strVal);

	char* trim(char* szOrg, int leng);

	// Encryption methods
	string encryptReceiver(const string& receiver, const string& encryptionMode);
	bool loadEncryptionConfig(const string& encryptionMode, const string& keyFilePath);
	void setEncryptionSettings(const string& encryptionMode, const string& keyFilePath);

private:
	AESEncryption aesEncryption;
	string encryptionMode;
	string keyFilePath;
	bool encryptionInitialized;
};

#endif /* PACKETCTRLSKY_H_ */
